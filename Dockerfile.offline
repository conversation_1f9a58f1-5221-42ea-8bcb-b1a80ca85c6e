# Dockerfile cho môi trường offline
FROM python:3.9-slim

WORKDIR /app

# Copy offline packages
COPY offline/pip_packages /tmp/pip_packages

# Install system dependencies (nếu cần)
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python packages từ offline cache
COPY requirements.txt .
RUN pip install --no-index --find-links /tmp/pip_packages -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY config.yaml .
COPY *.py .

# Create output directory
RUN mkdir -p output/csv output/st

# Set Python path
ENV PYTHONPATH=/app

# Default command
CMD ["python", "-m", "src.cli", "--help"]
