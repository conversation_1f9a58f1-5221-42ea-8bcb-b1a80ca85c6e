# Log Generator - Hướng dẫn Setup Offline

## Tổng quan
Hướng dẫn này giúp bạn setup Log Generator trên máy offline hoàn toàn.

## Chuẩn bị trên máy có internet

1. **Tải dependencies:**
   ```bash
   python3 setup_offline.py
   ```

2. **Tạo package offline:**
   ```bash
   tar -czf log_generator_offline.tar.gz src/ config.yaml requirements.txt offline/ setup_offline.* Dockerfile.offline README_OFFLINE.md
   ```

## Setup trên máy offline

### Linux/macOS:
```bash
# Giải nén package
tar -xzf log_generator_offline.tar.gz
cd log_generator_offline

# Chạy setup
./setup_offline.sh

# Sử dụng
source venv/bin/activate
python -m src.cli --help
```

### Windows:
```cmd
REM Giải nén package
tar -xzf log_generator_offline.tar.gz
cd log_generator_offline

REM Chạy setup
setup_offline.bat

REM Sử dụng
venv\Scripts\activate.bat
python -m src.cli --help
```

## Sử dụng Docker offline

```bash
# Build image từ offline packages
docker build -f Dockerfile.offline -t log-generator-offline .

# Chạy
docker run --rm -v $(pwd)/output:/app/output log-generator-offline python -m src.cli --app-name apf-dsn-flow --file-count 1 --size-mb 1
```

## Các lệnh sử dụng cơ bản

### Tạo log cho một ứng dụng:
```bash
python -m src.cli --app-name apf-dsn-flow --file-count 1 --size-mb 1
```

### Tạo log cho tất cả ứng dụng:
```bash
python -m src.cli --all-apps --file-count 2 --size-mb 5
```

### Dry run (không tạo file thực tế):
```bash
python -m src.cli --app-name apf-dsn-flow --file-count 1 --size-mb 1 --dry-run
```

### Tạo CSV với VIN data:
```bash
python -m src.vin_csv_generator --records 100000 --output output/csv/vehicle_region.csv
```

## Cấu trúc thư mục offline

```
offline/
└── pip_packages/           # Packages đã tải
    ├── PyYAML-6.0.2-*.whl
    ├── tqdm-4.67.1-*.whl
    ├── pytest-8.4.0-*.whl
    └── ...
```

## Troubleshooting

1. **Lỗi thiếu dependencies:**
   - Kiểm tra thư mục `offline/pip_packages`
   - Chạy lại `python3 setup_offline.py` trên máy có internet

2. **Lỗi permission:**
   ```bash
   chmod +x setup_offline.sh
   ```

3. **Lỗi Python version:**
   - Đảm bảo Python 3.7+ được cài đặt
   - Sử dụng `python3` thay vì `python` nếu cần

4. **Lỗi Docker:**
   - Đảm bảo Docker đã được cài đặt
   - Kiểm tra quyền truy cập Docker

## Ví dụ sử dụng hoàn chỉnh

```bash
# 1. Activate environment
source venv/bin/activate

# 2. Tạo CSV VIN data
python -m src.vin_csv_generator --records 100000

# 3. Tạo log files
python -m src.cli --app-name apf-dsn-flow --file-count 3 --size-mb 10

# 4. Kiểm tra output
ls -la output/st/base/logs/online/api-msg/
```

## Cấu hình

Chỉnh sửa file `config.yaml` để thay đổi:
- Định dạng log
- Biến số và giá trị
- Đường dẫn output
- Cấu hình timestamp

## Hỗ trợ

Nếu gặp vấn đề, kiểm tra:
1. Python version: `python --version`
2. Dependencies: `pip list`
3. File permissions: `ls -la`
4. Log files: kiểm tra thư mục `output/`
