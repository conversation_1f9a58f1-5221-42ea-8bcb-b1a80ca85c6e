#!/usr/bin/env python3
"""
Script để setup môi trường offline hoàn toàn cho log generator
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, check=True):
    """Chạy command và in output"""
    print(f"Đang chạy: {cmd}")
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    if check and result.returncode != 0:
        raise subprocess.CalledProcessError(result.returncode, cmd)
    return result

def download_dependencies():
    """Tải tất cả dependencies vào thư mục offline"""
    print("=== Tải dependencies offline ===")
    
    # Tạo thư mục offline
    offline_dir = Path("offline")
    offline_dir.mkdir(exist_ok=True)
    
    # Tải pip packages
    pip_dir = offline_dir / "pip_packages"
    pip_dir.mkdir(exist_ok=True)
    
    print("Tải pip packages...")
    run_command(f"pip download -r requirements.txt -d {pip_dir}")

def create_offline_dockerfile():
    """Tạo Dockerfile cho môi trường offline"""
    print("=== Tạo Dockerfile offline ===")
    
    dockerfile_content = '''# Dockerfile cho môi trường offline
FROM python:3.9-slim

WORKDIR /app

# Copy offline packages
COPY offline/pip_packages /tmp/pip_packages

# Install system dependencies (nếu cần)
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Install Python packages từ offline cache
COPY requirements.txt .
RUN pip install --no-index --find-links /tmp/pip_packages -r requirements.txt

# Copy source code
COPY src/ ./src/
COPY config.yaml .
COPY *.py .

# Create output directory
RUN mkdir -p output/csv output/st

# Set Python path
ENV PYTHONPATH=/app

# Default command
CMD ["python", "-m", "src.cli", "--help"]
'''
    
    with open("Dockerfile.offline", "w") as f:
        f.write(dockerfile_content)
    
    print("Đã tạo Dockerfile.offline")

def create_offline_setup_script():
    """Tạo script setup cho máy offline"""
    print("=== Tạo script setup offline ===")
    
    setup_script = '''#!/bin/bash
# Script setup cho máy offline

echo "=== Setup Log Generator Offline ==="

# Kiểm tra Python
if ! command -v python3 &> /dev/null; then
    echo "Lỗi: Python3 không được cài đặt"
    exit 1
fi

# Tạo virtual environment
echo "Tạo virtual environment..."
python3 -m venv venv
source venv/bin/activate

# Install packages từ offline cache
echo "Cài đặt packages từ offline cache..."
pip install --no-index --find-links offline/pip_packages -r requirements.txt

echo "Setup hoàn tất!"
echo "Để sử dụng:"
echo "1. source venv/bin/activate"
echo "2. python -m src.cli --help"
'''
    
    with open("setup_offline.sh", "w") as f:
        f.write(setup_script)
    
    os.chmod("setup_offline.sh", 0o755)
    print("Đã tạo setup_offline.sh")

def create_windows_setup_script():
    """Tạo script setup cho Windows"""
    print("=== Tạo script setup cho Windows ===")
    
    setup_script = '''@echo off
REM Script setup cho Windows offline

echo === Setup Log Generator Offline ===

REM Kiểm tra Python
python --version >nul 2>&1
if errorlevel 1 (
    echo Lỗi: Python không được cài đặt
    exit /b 1
)

REM Tạo virtual environment
echo Tạo virtual environment...
python -m venv venv
call venv\\Scripts\\activate.bat

REM Install packages từ offline cache
echo Cài đặt packages từ offline cache...
pip install --no-index --find-links offline\\pip_packages -r requirements.txt

echo Setup hoàn tất!
echo Để sử dụng:
echo 1. venv\\Scripts\\activate.bat
echo 2. python -m src.cli --help
'''
    
    with open("setup_offline.bat", "w") as f:
        f.write(setup_script)
    
    print("Đã tạo setup_offline.bat")

def main():
    """Main function"""
    print("=== Setup Log Generator Offline ===")
    
    try:
        download_dependencies()
        create_offline_dockerfile()
        create_offline_setup_script()
        create_windows_setup_script()
        
        print("\n=== Setup offline hoàn tất! ===")
        print("Các file đã tạo:")
        print("- offline/                 # Thư mục chứa dependencies")
        print("- Dockerfile.offline       # Dockerfile cho offline")
        print("- setup_offline.sh         # Script setup Linux/macOS")
        print("- setup_offline.bat        # Script setup Windows")
        
        print("\nBước tiếp theo:")
        print("1. Tạo package: tar -czf log_generator_offline.tar.gz src/ config.yaml requirements.txt offline/ setup_offline.* Dockerfile.offline")
        print("2. Copy package sang máy offline")
        print("3. Giải nén và chạy setup_offline.sh (Linux/macOS) hoặc setup_offline.bat (Windows)")
        
    except Exception as e:
        print(f"Lỗi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
